import { useCallback } from 'react';
import { useAuth } from './useAuth';
import { DatabaseService, PlantIdentification, PlantDiagnosis, UserProfile, GardenCollection, RecoveryTracking } from '@/services/database';
import { Plant } from '@/types/plant';

/**
 * Hook that provides authenticated database operations
 * This ensures all database calls use the proper Clerk JWT token for RLS policies
 */
export const useDatabase = () => {
  const { getSupabaseClient } = useAuth();

  // User Profile methods
  const getUserProfile = useCallback(async (userId: string): Promise<UserProfile | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.getUserProfile(userId, client);
  }, [getSupabaseClient]);

  const createUserProfile = useCallback(async (profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>): Promise<UserProfile | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.createUserProfile(profile, client);
  }, [getSupabaseClient]);

  const updateUserProfile = useCallback(async (userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.updateUserProfile(userId, updates, client);
  }, [getSupabaseClient]);

  const updateUserProfileStats = useCallback(async (userId: string): Promise<UserProfile | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.updateUserProfileStats(userId, client);
  }, [getSupabaseClient]);

  // Plant Identification methods
  const createPlantIdentification = useCallback(async (identification: Omit<PlantIdentification, 'id' | 'created_at' | 'updated_at'>): Promise<PlantIdentification> => {
    const client = await getSupabaseClient();
    return DatabaseService.createPlantIdentification(identification, client);
  }, [getSupabaseClient]);

  const getPlantIdentification = useCallback(async (id: string): Promise<PlantIdentification | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.getPlantIdentification(id, client);
  }, [getSupabaseClient]);

  const getPlantIdentifications = useCallback(async (userId: string): Promise<PlantIdentification[]> => {
    const client = await getSupabaseClient();
    return DatabaseService.getPlantIdentifications(userId, client);
  }, [getSupabaseClient]);

  const getRecentPlantIdentifications = useCallback(async (userId: string, limit: number = 5): Promise<PlantIdentification[]> => {
    const client = await getSupabaseClient();
    return DatabaseService.getRecentPlantIdentifications(userId, limit, client);
  }, [getSupabaseClient]);

  const updatePlantIdentification = useCallback(async (id: string, updates: Partial<PlantIdentification>): Promise<PlantIdentification | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.updatePlantIdentification(id, updates, client);
  }, [getSupabaseClient]);

  const deletePlantIdentification = useCallback(async (id: string): Promise<boolean> => {
    const client = await getSupabaseClient();
    return DatabaseService.deletePlantIdentification(id, client);
  }, [getSupabaseClient]);

  // Plant Diagnosis methods
  const createPlantDiagnosis = useCallback(async (diagnosis: Omit<PlantDiagnosis, 'id' | 'created_at' | 'updated_at'>): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClient();
    return DatabaseService.createPlantDiagnosis(diagnosis, client);
  }, [getSupabaseClient]);

  const getPlantDiagnosis = useCallback(async (id: string): Promise<PlantDiagnosis | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.getPlantDiagnosis(id, client);
  }, [getSupabaseClient]);

  const getPlantDiagnoses = useCallback(async (userId: string): Promise<PlantDiagnosis[]> => {
    const client = await getSupabaseClient();
    return DatabaseService.getPlantDiagnoses(userId, client);
  }, [getSupabaseClient]);

  const getRecentPlantDiagnoses = useCallback(async (userId: string, limit: number = 5): Promise<PlantDiagnosis[]> => {
    const client = await getSupabaseClient();
    return DatabaseService.getRecentPlantDiagnoses(userId, limit, client);
  }, [getSupabaseClient]);

  const updatePlantDiagnosis = useCallback(async (id: string, updates: Partial<PlantDiagnosis>): Promise<PlantDiagnosis | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.updatePlantDiagnosis(id, updates, client);
  }, [getSupabaseClient]);

  const deletePlantDiagnosis = useCallback(async (id: string): Promise<boolean> => {
    const client = await getSupabaseClient();
    return DatabaseService.deletePlantDiagnosis(id, client);
  }, [getSupabaseClient]);

  const updateDiagnosisNotes = useCallback(async (diagnosisId: string, notes: string): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClient();
    return DatabaseService.updateDiagnosisNotes(diagnosisId, notes, client);
  }, [getSupabaseClient]);

  // Garden Collection methods
  const addToGarden = useCallback(async (gardenItem: Omit<GardenCollection, 'id' | 'created_at' | 'updated_at'>): Promise<GardenCollection | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.addToGarden(gardenItem, client);
  }, [getSupabaseClient]);

  const getGardenItems = useCallback(async (userId: string): Promise<GardenCollection[]> => {
    const client = await getSupabaseClient();
    return DatabaseService.getGardenItems(userId, client);
  }, [getSupabaseClient]);

  const removeFromGarden = useCallback(async (id: string): Promise<boolean> => {
    const client = await getSupabaseClient();
    return DatabaseService.removeFromGarden(id, client);
  }, [getSupabaseClient]);

  // High-level methods for plant operations
  const createIdentificationAndAddToGarden = useCallback(async (plant: Plant, imageUri: string, userId: string, notes?: string, location?: string): Promise<PlantIdentification> => {
    const client = await getSupabaseClient();
    return DatabaseService.createIdentificationAndAddToGarden(plant, imageUri, userId, notes, location, client);
  }, [getSupabaseClient]);

  const createDiagnosisOnly = useCallback(async (plant: Plant, imageUri: string, userId: string, diagnosisData: any, notes?: string, location?: string): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClient();
    return DatabaseService.createDiagnosisOnly(plant, imageUri, userId, diagnosisData, notes, location, client);
  }, [getSupabaseClient]);

  const shareIdentificationOnly = useCallback(async (plant: Plant, imageUri: string, userId: string): Promise<PlantIdentification> => {
    const client = await getSupabaseClient();
    return DatabaseService.shareIdentificationOnly(plant, imageUri, userId, client);
  }, [getSupabaseClient]);

  const shareDiagnosisOnly = useCallback(async (plant: Plant, imageUri: string, userId: string, diagnosisData: any): Promise<PlantDiagnosis> => {
    const client = await getSupabaseClient();
    return DatabaseService.shareDiagnosisOnly(plant, imageUri, userId, diagnosisData, client);
  }, [getSupabaseClient]);

  const addToGardenAndShare = useCallback(async (gardenItem: Omit<GardenCollection, 'id' | 'created_at' | 'updated_at'>): Promise<GardenCollection | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.addToGardenAndShare(gardenItem, client);
  }, [getSupabaseClient]);

  // Recovery Tracking methods
  const createRecoveryTracking = useCallback(async (tracking: Omit<RecoveryTracking, 'id' | 'created_at' | 'updated_at'>): Promise<RecoveryTracking | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.createRecoveryTracking(tracking, client);
  }, [getSupabaseClient]);

  const getRecoveryTracking = useCallback(async (diagnosisId: string): Promise<RecoveryTracking[]> => {
    const client = await getSupabaseClient();
    return DatabaseService.getRecoveryTracking(diagnosisId, client);
  }, [getSupabaseClient]);

  const updateRecoveryTracking = useCallback(async (id: string, updates: Partial<RecoveryTracking>): Promise<RecoveryTracking | null> => {
    const client = await getSupabaseClient();
    return DatabaseService.updateRecoveryTracking(id, updates, client);
  }, [getSupabaseClient]);

  return {
    // User Profile methods
    getUserProfile,
    createUserProfile,
    updateUserProfile,
    updateUserProfileStats,
    
    // Plant Identification methods
    createPlantIdentification,
    getPlantIdentification,
    getPlantIdentifications,
    getRecentPlantIdentifications,
    updatePlantIdentification,
    deletePlantIdentification,

    // Plant Diagnosis methods
    createPlantDiagnosis,
    getPlantDiagnosis,
    getPlantDiagnoses,
    getRecentPlantDiagnoses,
    updatePlantDiagnosis,
    deletePlantDiagnosis,
    updateDiagnosisNotes,
    
    // Garden Collection methods
    addToGarden,
    getGardenItems,
    removeFromGarden,
    
    // High-level methods
    createIdentificationAndAddToGarden,
    createDiagnosisOnly,
    shareIdentificationOnly,
    shareDiagnosisOnly,
    addToGardenAndShare,
    
    // Recovery Tracking methods
    createRecoveryTracking,
    getRecoveryTracking,
    updateRecoveryTracking,
  };
};
